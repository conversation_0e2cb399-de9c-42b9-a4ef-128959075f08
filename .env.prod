# 生产环境配置文件
# 移除MySQL数据库配置 - 数据库操作由Java后端负责

# Redis配置
REDIS_HOST=**************
REDIS_PORT=26379

# RabbitMQ配置
RABBITMQ_HOST=**************
RABBITMQ_PORT=25674
RABBITMQ_ACCOUNT=admin
RABBITMQ_PASSWORD=vipa@404

# RabbitMQ连接配置
RABBITMQ_CONNECTION_TIMEOUT=60
RABBITMQ_HEARTBEAT=300
RABBITMQ_SOCKET_TIMEOUT=10
RABBITMQ_BLOCKED_CONNECTION_TIMEOUT=300
RABBITMQ_CONNECTION_ATTEMPTS=3
RABBITMQ_RETRY_DELAY=1

# 应用配置
DEBUG_FLAG=false
# 移除HOST_UPLOADS配置 - 前端相关配置不再需要

# 文件路径配置
PROJECT_SAVE_DIR=/nfs5/medlabel/medlabel_212/projects

# 环境标识
ENVIRONMENT=prod
